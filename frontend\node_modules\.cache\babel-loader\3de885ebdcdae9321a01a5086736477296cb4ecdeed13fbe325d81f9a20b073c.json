{"ast": null, "code": "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getPickersArrowSwitcherUtilityClass(slot) {\n  return generateUtilityClass('MuiPickersArrowSwitcher', slot);\n}\nexport const pickersArrowSwitcherClasses = generateUtilityClasses('MuiPickersArrowSwitcher', ['root', 'spacer', 'button', 'previousIconButton', 'nextIconButton', 'leftArrowIcon', 'rightArrowIcon']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getPickersArrowSwitcherUtilityClass", "slot", "pickersArrowSwitcherClasses"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/EmployeeManagementSystem/frontend/node_modules/@mui/x-date-pickers/esm/internals/components/PickersArrowSwitcher/pickersArrowSwitcherClasses.js"], "sourcesContent": ["import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getPickersArrowSwitcherUtilityClass(slot) {\n  return generateUtilityClass('MuiPickersArrowSwitcher', slot);\n}\nexport const pickersArrowSwitcherClasses = generateUtilityClasses('MuiPickersArrowSwitcher', ['root', 'spacer', 'button', 'previousIconButton', 'nextIconButton', 'leftArrowIcon', 'rightArrowIcon']);"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,sBAAsB,MAAM,mCAAmC;AACtE,OAAO,SAASC,mCAAmCA,CAACC,IAAI,EAAE;EACxD,OAAOH,oBAAoB,CAAC,yBAAyB,EAAEG,IAAI,CAAC;AAC9D;AACA,OAAO,MAAMC,2BAA2B,GAAGH,sBAAsB,CAAC,yBAAyB,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,eAAe,EAAE,gBAAgB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}