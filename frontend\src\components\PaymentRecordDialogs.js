import {
    Add as AddIcon,
    Close as CloseIcon,
    Remove as RemoveIcon
} from '@mui/icons-material';
import {
    Box,
    Button,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Divider,
    FormControl,
    Grid,
    IconButton,
    InputAdornment,
    InputLabel,
    MenuItem,
    Paper,
    Select,
    TextField,
    Typography
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { memo, useCallback } from 'react';

// Create/Edit Dialog Component - moved outside to prevent re-creation
const CreateEditDialog = memo(({
  open,
  onClose,
  isEdit = false,
  formData,
  setFormData,
  employees,
  handleCreateRecord,
  handleUpdateRecord,
  addBonus,
  removeBonus,
  updateBonus,
  addDeduction,
  removeDeduction,
  updateDeduction
}) => {

  // Memoized event handlers to prevent re-creation
  const handleBasicSalaryChange = useCallback((e) => {
    setFormData(prev => ({ ...prev, basicSalary: e.target.value }));
  }, [setFormData]);

  const handlePaymentMethodChange = useCallback((e) => {
    setFormData(prev => ({ ...prev, paymentMethod: e.target.value }));
  }, [setFormData]);

  const handleOvertimeHoursChange = useCallback((e) => {
    setFormData(prev => ({
      ...prev,
      overtime: { ...prev.overtime, hours: e.target.value }
    }));
  }, [setFormData]);

  const handleOvertimeRateChange = useCallback((e) => {
    setFormData(prev => ({
      ...prev,
      overtime: { ...prev.overtime, rate: e.target.value }
    }));
  }, [setFormData]);

  const handleNotesChange = useCallback((e) => {
    setFormData(prev => ({ ...prev, notes: e.target.value }));
  }, [setFormData]);

  const handleEmployeeChange = useCallback((e) => {
    setFormData(prev => ({ ...prev, employeeId: e.target.value }));
  }, [setFormData]);

  const handleWeekStartDateChange = useCallback((date) => {
    setFormData(prev => ({ ...prev, weekStartDate: date }));
  }, [setFormData]);

  const handleWeekEndDateChange = useCallback((date) => {
    setFormData(prev => ({ ...prev, weekEndDate: date }));
  }, [setFormData]);

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            {isEdit ? 'Edit Payment Record' : 'Create Payment Record'}
          </Typography>
          <IconButton onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent>
        <Box sx={{ pt: 2 }}>
          <Grid container spacing={3}>
            {/* Employee Selection (only for create) */}
            {!isEdit && (
              <Grid item xs={12}>
                <FormControl fullWidth required>
                  <InputLabel>Employee</InputLabel>
                  <Select
                    value={formData.employeeId}
                    label="Employee"
                    onChange={handleEmployeeChange}
                  >
                    {employees.map((employee) => (
                      <MenuItem key={employee._id} value={employee._id}>
                        {employee.user?.name} ({employee.employeeId})
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            )}

            {/* Week Period */}
            <Grid item xs={12} sm={6}>
              <DatePicker
                label="Week Start Date *"
                value={formData.weekStartDate}
                onChange={handleWeekStartDateChange}
                renderInput={(params) => <TextField {...params} fullWidth required />}
                disabled={isEdit}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <DatePicker
                label="Week End Date *"
                value={formData.weekEndDate}
                onChange={handleWeekEndDateChange}
                renderInput={(params) => <TextField {...params} fullWidth required />}
                disabled={isEdit}
              />
            </Grid>

            {/* Basic Salary */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Basic Salary"
                type="number"
                value={formData.basicSalary}
                onChange={handleBasicSalaryChange}
                InputProps={{
                  startAdornment: <InputAdornment position="start">$</InputAdornment>,
                }}
                required
              />
            </Grid>

            {/* Payment Method */}
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Payment Method</InputLabel>
                <Select
                  value={formData.paymentMethod}
                  label="Payment Method"
                  onChange={handlePaymentMethodChange}
                >
                  <MenuItem value="bank_transfer">Bank Transfer</MenuItem>
                  <MenuItem value="cash">Cash</MenuItem>
                  <MenuItem value="check">Check</MenuItem>
                  <MenuItem value="other">Other</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* Overtime Section */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                Overtime
              </Typography>
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Overtime Hours"
                type="number"
                value={formData.overtime.hours}
                onChange={handleOvertimeHoursChange}
                inputProps={{ step: "0.5", min: "0" }}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Overtime Rate"
                type="number"
                value={formData.overtime.rate}
                onChange={handleOvertimeRateChange}
                InputProps={{
                  startAdornment: <InputAdornment position="start">$</InputAdornment>,
                }}
                inputProps={{ step: "0.01", min: "0" }}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Overtime Amount"
                type="number"
                value={(parseFloat(formData.overtime.hours) || 0) * (parseFloat(formData.overtime.rate) || 0)}
                InputProps={{
                  startAdornment: <InputAdornment position="start">$</InputAdornment>,
                  readOnly: true
                }}
                sx={{ backgroundColor: '#f8fafc' }}
              />
            </Grid>

            {/* Bonuses Section */}
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Bonuses
                </Typography>
                <Button
                  startIcon={<AddIcon />}
                  onClick={addBonus}
                  size="small"
                  variant="outlined"
                >
                  Add Bonus
                </Button>
              </Box>
              {formData.bonuses.map((bonus, index) => (
                <Box key={index} sx={{ mb: 2, p: 2, border: '1px solid #e2e8f0', borderRadius: 1 }}>
                  <Grid container spacing={2} alignItems="center">
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Bonus Description"
                        value={bonus.description}
                        onChange={(e) => updateBonus(index, 'description', e.target.value)}
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <TextField
                        fullWidth
                        label="Amount"
                        type="number"
                        value={bonus.amount}
                        onChange={(e) => updateBonus(index, 'amount', e.target.value)}
                        InputProps={{
                          startAdornment: <InputAdornment position="start">$</InputAdornment>,
                        }}
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={12} sm={2}>
                      <IconButton
                        color="error"
                        onClick={() => removeBonus(index)}
                        size="small"
                      >
                        <RemoveIcon />
                      </IconButton>
                    </Grid>
                  </Grid>
                </Box>
              ))}
            </Grid>

            {/* Deductions Section */}
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Deductions
                </Typography>
                <Button
                  startIcon={<AddIcon />}
                  onClick={addDeduction}
                  size="small"
                  variant="outlined"
                >
                  Add Deduction
                </Button>
              </Box>
              {formData.deductions.map((deduction, index) => (
                <Box key={index} sx={{ mb: 2, p: 2, border: '1px solid #e2e8f0', borderRadius: 1 }}>
                  <Grid container spacing={2} alignItems="center">
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Deduction Description"
                        value={deduction.description}
                        onChange={(e) => updateDeduction(index, 'description', e.target.value)}
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <TextField
                        fullWidth
                        label="Amount"
                        type="number"
                        value={deduction.amount}
                        onChange={(e) => updateDeduction(index, 'amount', e.target.value)}
                        InputProps={{
                          startAdornment: <InputAdornment position="start">$</InputAdornment>,
                        }}
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={12} sm={2}>
                      <IconButton
                        color="error"
                        onClick={() => removeDeduction(index)}
                        size="small"
                      >
                        <RemoveIcon />
                      </IconButton>
                    </Grid>
                  </Grid>
                </Box>
              ))}
            </Grid>

            {/* Notes */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Notes"
                value={formData.notes}
                onChange={handleNotesChange}
                placeholder="Additional notes or comments..."
              />
            </Grid>
          </Grid>
        </Box>
      </DialogContent>
      <DialogActions sx={{ p: 3 }}>
        <Button onClick={onClose}>
          Cancel
        </Button>
        <Button
          variant="contained"
          onClick={isEdit ? handleUpdateRecord : handleCreateRecord}
          sx={{
            background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
            '&:hover': {
              background: 'linear-gradient(135deg, #059669 0%, #047857 100%)',
            }
          }}
        >
          {isEdit ? 'Update Record' : 'Create Record'}
        </Button>
      </DialogActions>
    </Dialog>
  );
});

// View Dialog Component - also moved outside to prevent re-creation
const ViewDialog = memo(({
  viewDialog,
  setViewDialog,
  selectedRecord,
  formatCurrency,
  formatDate
}) => (
    <Dialog open={viewDialog} onClose={() => setViewDialog(false)} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Payment Record Details
          </Typography>
          <IconButton onClick={() => setViewDialog(false)}>
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent>
        {selectedRecord && (
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={3}>
              {/* Employee Info */}
              <Grid item xs={12}>
                <Paper sx={{ p: 2, backgroundColor: '#f8fafc' }}>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                    Employee Information
                  </Typography>
                  <Typography variant="body2">
                    <strong>Name:</strong> {selectedRecord.employee?.user?.name || 'Unknown'}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Employee ID:</strong> {selectedRecord.employee?.employeeId || 'N/A'}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Department:</strong> {selectedRecord.employee?.user?.department || 'N/A'}
                  </Typography>
                </Paper>
              </Grid>

              {/* Pay Period */}
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                  Pay Period
                </Typography>
                <Typography variant="body2">
                  {formatDate(selectedRecord.weekStartDate)} - {formatDate(selectedRecord.weekEndDate)}
                </Typography>
              </Grid>

              {/* Payment Status */}
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                  Payment Status
                </Typography>
                <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                  {selectedRecord.paymentStatus}
                </Typography>
              </Grid>

              {/* Salary Breakdown */}
              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                  Salary Breakdown
                </Typography>
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                  Basic Salary
                </Typography>
                <Typography variant="h6" sx={{ color: '#059669' }}>
                  {formatCurrency(selectedRecord.basicSalary)}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                  Overtime
                </Typography>
                <Typography variant="body2">
                  {selectedRecord.overtime.hours} hrs × {formatCurrency(selectedRecord.overtime.rate)}
                </Typography>
                <Typography variant="h6" sx={{ color: '#3b82f6' }}>
                  {formatCurrency(selectedRecord.overtime.amount)}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                  Gross Pay
                </Typography>
                <Typography variant="h6" sx={{ color: '#1e293b' }}>
                  {formatCurrency(selectedRecord.grossPay)}
                </Typography>
              </Grid>

              {/* Bonuses */}
              {selectedRecord.bonuses && selectedRecord.bonuses.length > 0 && (
                <Grid item xs={12}>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                    Bonuses
                  </Typography>
                  {selectedRecord.bonuses.map((bonus, index) => (
                    <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">{bonus.description}</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 600, color: '#059669' }}>
                        {formatCurrency(bonus.amount)}
                      </Typography>
                    </Box>
                  ))}
                </Grid>
              )}

              {/* Deductions */}
              {selectedRecord.deductions && selectedRecord.deductions.length > 0 && (
                <Grid item xs={12}>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                    Deductions
                  </Typography>
                  {selectedRecord.deductions.map((deduction, index) => (
                    <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">{deduction.description}</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 600, color: '#ef4444' }}>
                        -{formatCurrency(deduction.amount)}
                      </Typography>
                    </Box>
                  ))}
                </Grid>
              )}

              {/* Net Pay */}
              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="h5" sx={{ fontWeight: 700 }}>
                    Net Pay
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: '#10b981' }}>
                    {formatCurrency(selectedRecord.netPay)}
                  </Typography>
                </Box>
              </Grid>

              {/* Notes */}
              {selectedRecord.notes && (
                <Grid item xs={12}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                    Notes
                  </Typography>
                  <Paper sx={{ p: 2, backgroundColor: '#f8fafc' }}>
                    <Typography variant="body2">
                      {selectedRecord.notes}
                    </Typography>
                  </Paper>
                </Grid>
              )}
            </Grid>
          </Box>
        )}
      </DialogContent>
      <DialogActions sx={{ p: 3 }}>
        <Button onClick={() => setViewDialog(false)}>
          Close
        </Button>
      </DialogActions>
    </Dialog>
));

// Main PaymentRecordDialogs Component
const PaymentRecordDialogs = memo(({
  openDialog,
  setOpenDialog,
  editDialog,
  setEditDialog,
  viewDialog,
  setViewDialog,
  formData,
  setFormData,
  selectedRecord,
  employees,
  handleCreateRecord,
  handleUpdateRecord,
  addBonus,
  removeBonus,
  updateBonus,
  addDeduction,
  removeDeduction,
  updateDeduction,
  resetForm,
  formatCurrency,
  formatDate
}) => {

  // Memoized close handlers to prevent re-creation
  const handleCreateDialogClose = useCallback(() => {
    setOpenDialog(false);
    resetForm();
  }, [setOpenDialog, resetForm]);

  const handleEditDialogClose = useCallback(() => {
    setEditDialog(false);
    resetForm();
  }, [setEditDialog, resetForm]);

  return (
    <>
      {/* Create Dialog */}
      <CreateEditDialog
        open={openDialog}
        onClose={handleCreateDialogClose}
        isEdit={false}
        formData={formData}
        setFormData={setFormData}
        employees={employees}
        handleCreateRecord={handleCreateRecord}
        handleUpdateRecord={handleUpdateRecord}
        addBonus={addBonus}
        removeBonus={removeBonus}
        updateBonus={updateBonus}
        addDeduction={addDeduction}
        removeDeduction={removeDeduction}
        updateDeduction={updateDeduction}
      />

      {/* Edit Dialog */}
      <CreateEditDialog
        open={editDialog}
        onClose={handleEditDialogClose}
        isEdit={true}
        formData={formData}
        setFormData={setFormData}
        employees={employees}
        handleCreateRecord={handleCreateRecord}
        handleUpdateRecord={handleUpdateRecord}
        addBonus={addBonus}
        removeBonus={removeBonus}
        updateBonus={updateBonus}
        addDeduction={addDeduction}
        removeDeduction={removeDeduction}
        updateDeduction={updateDeduction}
      />

      {/* View Dialog */}
      <ViewDialog
        viewDialog={viewDialog}
        setViewDialog={setViewDialog}
        selectedRecord={selectedRecord}
        formatCurrency={formatCurrency}
        formatDate={formatDate}
      />
    </>
  );
});

export default PaymentRecordDialogs;
